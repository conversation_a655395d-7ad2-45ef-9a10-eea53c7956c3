import React, { useState, useRef, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';

export interface MessageInputProps {
  placeholder?: string;
  channelId?: string;
  threadId?: string;
  replyToMessageId?: string;
  onSendMessage: (content: string, attachments?: File[]) => void;
  onTyping?: (isTyping: boolean) => void;
  onFileUpload?: (files: File[]) => void;
  disabled?: boolean;
  maxLength?: number;
  className?: string;
  'data-testid'?: string;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  placeholder = 'Type a message...',
  channelId,
  threadId,
  replyToMessageId,
  onSendMessage,
  onTyping,
  onFileUpload,
  disabled = false,
  maxLength = 4000,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [attachments, setAttachments] = useState<File[]>([]);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  }, [message]);

  // Handle typing indicator
  useEffect(() => {
    if (message.trim() && !isTyping) {
      setIsTyping(true);
      onTyping?.(true);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        onTyping?.(false);
      }
    }, 2000);

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [message, isTyping, onTyping]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim() && attachments.length === 0) return;
    if (disabled) return;

    onSendMessage(message.trim(), attachments);
    setMessage('');
    setAttachments([]);
    
    // Stop typing indicator
    if (isTyping) {
      setIsTyping(false);
      onTyping?.(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      setAttachments(prev => [...prev, ...files]);
      onFileUpload?.(files);
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const insertEmoji = (emoji: string) => {
    setMessage(prev => prev + emoji);
    setShowEmojiPicker(false);
    textareaRef.current?.focus();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const commonEmojis = ['😊', '👍', '❤️', '😂', '😮', '😢', '🎉', '🚀', '💯', '🔥'];

  return (
    <div
      className={`border-t ${className}`}
      style={{
        backgroundColor: colors.surface,
        borderTopColor: colors.border,
      }}
      data-testid={testId}
    >
      {/* Attachments Preview */}
      {attachments.length > 0 && (
        <div className="p-4 border-b" style={{ borderBottomColor: colors.border }}>
          <div className="flex flex-wrap gap-2">
            {attachments.map((file, index) => (
              <div
                key={index}
                className="flex items-center space-x-2 px-3 py-2 rounded-lg"
                style={{
                  backgroundColor: colors.backgroundSecondary,
                  borderColor: colors.border,
                }}
              >
                <span className="text-sm">
                  {file.type.startsWith('image/') ? '🖼️' :
                   file.type.startsWith('video/') ? '🎥' :
                   file.type.startsWith('audio/') ? '🎵' : '📄'}
                </span>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate" style={{ color: colors.text }}>
                    {file.name}
                  </p>
                  <p className="text-xs" style={{ color: colors.textSecondary }}>
                    {formatFileSize(file.size)}
                  </p>
                </div>
                <button
                  onClick={() => removeAttachment(index)}
                  className="text-red-500 hover:text-red-700 text-sm"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Main Input Area */}
      <div className="p-4">
        <form onSubmit={handleSubmit} className="space-y-3">
          {/* Text Input */}
          <div className="relative">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              maxLength={maxLength}
              rows={1}
              className="w-full resize-none border rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500"
              style={{
                borderColor: colors.border,
                backgroundColor: colors.background,
                color: colors.text,
              }}
            />
            
            {/* Send Button */}
            <button
              type="submit"
              disabled={disabled || (!message.trim() && attachments.length === 0)}
              className="absolute right-2 bottom-2 p-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                backgroundColor: (!message.trim() && attachments.length === 0) ? colors.backgroundSecondary : colors.primary,
                color: (!message.trim() && attachments.length === 0) ? colors.textSecondary : 'white',
              }}
            >
              ➤
            </button>
          </div>

          {/* Toolbar */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {/* File Upload */}
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                disabled={disabled}
                className="flex items-center space-x-1 text-sm hover:underline disabled:opacity-50"
                style={{ color: colors.textSecondary }}
              >
                <span>📎</span>
                <span>Attach</span>
              </button>

              {/* Emoji Picker */}
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  disabled={disabled}
                  className="flex items-center space-x-1 text-sm hover:underline disabled:opacity-50"
                  style={{ color: colors.textSecondary }}
                >
                  <span>😊</span>
                  <span>Emoji</span>
                </button>

                {/* Emoji Picker Dropdown */}
                {showEmojiPicker && (
                  <div
                    className="absolute bottom-full left-0 mb-2 p-3 rounded-lg shadow-lg border z-10"
                    style={{
                      backgroundColor: colors.surface,
                      borderColor: colors.border,
                    }}
                  >
                    <div className="grid grid-cols-5 gap-2">
                      {commonEmojis.map((emoji) => (
                        <button
                          key={emoji}
                          type="button"
                          onClick={() => insertEmoji(emoji)}
                          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-lg"
                        >
                          {emoji}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Format Options */}
              <button
                type="button"
                disabled={disabled}
                className="flex items-center space-x-1 text-sm hover:underline disabled:opacity-50"
                style={{ color: colors.textSecondary }}
              >
                <span>📝</span>
                <span>Format</span>
              </button>
            </div>

            {/* Character Count & Send Hint */}
            <div className="flex items-center space-x-4">
              {message.length > maxLength * 0.8 && (
                <span
                  className={`text-xs ${message.length >= maxLength ? 'text-red-500' : ''}`}
                  style={{ color: message.length >= maxLength ? 'red' : colors.textSecondary }}
                >
                  {message.length}/{maxLength}
                </span>
              )}
              <span className="text-xs" style={{ color: colors.textSecondary }}>
                Press Enter to send
              </span>
            </div>
          </div>
        </form>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>
    </div>
  );
};
